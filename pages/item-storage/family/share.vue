<template>
    <view class="page">
        
    </view>
</template>

<script>
const app = getApp()

export default {
    data() {
        return {
            ownerInfo: {
                nickname: '',
                headimg: ''
            }
        }
    },
    
    onLoad(params) {
        this.uuid = params.uuid
        this.type = Number(params.type)
        this.$login.uniLogin(() => {
            this.init()
        })
    },

    methods: {
        async init() {
            await this.getOwnerInfo()
        },

        async getOwnerInfo() {
            const res = await this.xwy_api.request({
                url: 'front.user.user/user_details',
                data: {
                    userid: this.uuid
                }
            })
            
            const info = res?.data?.user_details
            if (!info?.id) {
                this.$uni.showModal('邀请失效', {success: () => this.$uni.reLaunch('/pages/item-storage/item/list')})
                return
            }

            this.ownerInfo = {
                nickname: info.nickname || '',
                headimg: info.headimg || ''
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.share-page {
    min-height: 100vh;
    background-color: #f5f5f5;

    &__container {
        padding: 120rpx 60rpx 60rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    // 头像区域样式
    &__avatar-section {
        margin-bottom: 80rpx;
    }

    &__avatar-wrapper {
        width: 200rpx;
        height: 200rpx;
        border-radius: 50%;
        border: 6rpx solid #ff4444;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        background-color: #ffffff;
    }

    &__avatar {
        width: 100%;
        height: 100%;
        border-radius: 50%;
    }

    &__avatar-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f8f8f8;
        border-radius: 50%;
    }

    &__avatar-text {
        font-size: 28rpx;
        color: #999999;
    }

    // 邀请文本区域样式
    &__invite-section {
        margin-bottom: 120rpx;
        text-align: center;
    }

    &__invite-text {
        font-size: 32rpx;
        color: #333333;
        line-height: 1.5;
        font-weight: 500;
    }

    // 昵称输入区域样式
    &__input-section {
        width: 100%;
        margin-bottom: 100rpx;
    }

    &__input-label {
        font-size: 28rpx;
        color: #666666;
        margin-bottom: 20rpx;
        display: block;
    }

    &__input-wrapper {
        width: 100%;
        border: 2rpx solid #e0e0e0;
        border-radius: 12rpx;
        background-color: #ffffff;
        overflow: hidden;
    }

    &__input {
        width: 100%;
        height: 88rpx;
        padding: 0 24rpx;
        font-size: 28rpx;
        color: #333333;
        background-color: transparent;
        border: none;
        outline: none;

        &::placeholder {
            color: #cccccc;
        }
    }

    &__input-placeholder {
        color: #cccccc;
    }

    // 按钮区域样式
    &__button-section {
        width: 100%;
    }

    &__accept-btn {
        width: 100%;
        height: 88rpx;
        background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
        border-radius: 44rpx;
        border: none;
        color: #ffffff;
        font-size: 32rpx;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 8rpx 24rpx rgba(74, 144, 226, 0.3);
        transition: all 0.3s ease;

        &:active {
            transform: translateY(2rpx);
            box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
        }

        &--disabled {
            background: #cccccc;
            box-shadow: none;

            &:active {
                transform: none;
            }
        }
    }
}

// 全局样式重置
page {
    background-color: #f5f5f5;
}
</style>
